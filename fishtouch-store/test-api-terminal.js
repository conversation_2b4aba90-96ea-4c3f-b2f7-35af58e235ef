#!/usr/bin/env node

/**
 * 终端API测试脚本
 * 用于在终端中测试FishTouch Store的API功能
 */

import fs from 'fs';
import path from 'path';

// 模拟Tauri API调用的测试数据
const testResults = {
    packages: [],
    items: [],
    batches: [],
    inventory: [],
    alerts: []
};

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logHeader(message) {
    log(`\n🔧 ${message}`, 'cyan');
    log('='.repeat(50), 'cyan');
}

// 检查项目结构
function checkProjectStructure() {
    logHeader('检查项目结构');

    const requiredFiles = [
        'src-tauri/Cargo.toml',
        'src-tauri/src/lib.rs',
        'src-tauri/src/main.rs',
        'src/main.tsx',
        'src/services/api.ts',
        'package.json'
    ];

    let allFilesExist = true;

    for (const file of requiredFiles) {
        if (fs.existsSync(file)) {
            logSuccess(`文件存在: ${file}`);
        } else {
            logError(`文件缺失: ${file}`);
            allFilesExist = false;
        }
    }

    return allFilesExist;
}

// 检查依赖配置
function checkDependencies() {
    logHeader('检查依赖配置');

    try {
        // 检查package.json
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        logSuccess(`前端项目名称: ${packageJson.name}`);
        logInfo(`React版本: ${packageJson.dependencies?.react || '未找到'}`);
        logInfo(`Tauri版本: ${packageJson.devDependencies?.['@tauri-apps/cli'] || '未找到'}`);

        // 检查Cargo.toml
        const cargoToml = fs.readFileSync('src-tauri/Cargo.toml', 'utf8');
        if (cargoToml.includes('tauri')) {
            logSuccess('Tauri后端配置正确');
        }
        if (cargoToml.includes('diesel')) {
            logSuccess('Diesel ORM配置正确');
        }
        if (cargoToml.includes('sqlite')) {
            logSuccess('SQLite数据库配置正确');
        }

        return true;
    } catch (error) {
        logError(`依赖检查失败: ${error.message}`);
        return false;
    }
}

// 模拟API测试
function simulateApiTests() {
    logHeader('模拟API功能测试');

    // 模拟套餐测试
    logInfo('测试套餐管理功能...');
    const testPackage = {
        id: 1,
        name: '测试套餐_' + Date.now(),
        description: '这是一个测试套餐'
    };
    testResults.packages.push(testPackage);
    logSuccess(`套餐创建模拟成功: ${testPackage.name}`);

    // 模拟单品测试
    logInfo('测试单品管理功能...');
    const testItem = {
        id: 1,
        name: '测试单品_' + Date.now(),
        sku: 'TEST_' + Date.now(),
        package_id: testPackage.id
    };
    testResults.items.push(testItem);
    logSuccess(`单品创建模拟成功: ${testItem.name} (SKU: ${testItem.sku})`);

    // 模拟批次测试
    logInfo('测试批次管理功能...');
    const testBatch = {
        id: 1,
        item_id: testItem.id,
        batch_number: 'BATCH_' + Date.now(),
        in_date: new Date().toISOString().split('T')[0],
        expiry_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    };
    testResults.batches.push(testBatch);
    logSuccess(`批次创建模拟成功: ${testBatch.batch_number}`);

    // 模拟库存测试
    logInfo('测试库存管理功能...');
    const testInventory = {
        item_id: testItem.id,
        batch_id: testBatch.id,
        current_quantity: 100,
        total_in: 150,
        total_out: 50
    };
    testResults.inventory.push(testInventory);
    logSuccess(`库存记录模拟成功: 当前库存 ${testInventory.current_quantity} 件`);

    // 模拟预警测试
    logInfo('测试库存预警功能...');
    const testAlert = {
        item_id: testItem.id,
        threshold: 20,
        current_stock: testInventory.current_quantity,
        is_triggered: testInventory.current_quantity < 20
    };
    testResults.alerts.push(testAlert);

    if (testAlert.is_triggered) {
        logWarning(`库存预警触发: ${testItem.name} 当前库存 ${testAlert.current_stock} 低于阈值 ${testAlert.threshold}`);
    } else {
        logSuccess(`库存预警正常: ${testItem.name} 当前库存 ${testAlert.current_stock} 高于阈值 ${testAlert.threshold}`);
    }
}

// 检查数据库文件
function checkDatabase() {
    logHeader('检查数据库配置');

    const dbPath = 'src-tauri/database.db';
    if (fs.existsSync(dbPath)) {
        const stats = fs.statSync(dbPath);
        logSuccess(`数据库文件存在: ${dbPath}`);
        logInfo(`文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
        logInfo(`最后修改: ${stats.mtime.toLocaleString()}`);
    } else {
        logWarning('数据库文件不存在，将在首次运行时创建');
    }

    // 检查迁移文件
    const migrationsPath = 'src-tauri/migrations';
    if (fs.existsSync(migrationsPath)) {
        const migrations = fs.readdirSync(migrationsPath);
        logSuccess(`找到 ${migrations.length} 个数据库迁移文件`);
        migrations.forEach(migration => {
            logInfo(`  - ${migration}`);
        });
    } else {
        logWarning('未找到数据库迁移文件夹');
    }
}

// 生成测试报告
function generateTestReport() {
    logHeader('测试报告总结');

    log('\n📊 模拟测试数据统计:', 'bright');
    log(`套餐数量: ${testResults.packages.length}`, 'cyan');
    log(`单品数量: ${testResults.items.length}`, 'cyan');
    log(`批次数量: ${testResults.batches.length}`, 'cyan');
    log(`库存记录: ${testResults.inventory.length}`, 'cyan');
    log(`预警记录: ${testResults.alerts.length}`, 'cyan');

    log('\n🔍 详细数据:', 'bright');
    if (testResults.packages.length > 0) {
        log('套餐信息:', 'yellow');
        testResults.packages.forEach(pkg => {
            log(`  - ID: ${pkg.id}, 名称: ${pkg.name}`, 'reset');
        });
    }

    if (testResults.items.length > 0) {
        log('单品信息:', 'yellow');
        testResults.items.forEach(item => {
            log(`  - ID: ${item.id}, 名称: ${item.name}, SKU: ${item.sku}`, 'reset');
        });
    }

    if (testResults.batches.length > 0) {
        log('批次信息:', 'yellow');
        testResults.batches.forEach(batch => {
            log(`  - 批次号: ${batch.batch_number}, 入库日期: ${batch.in_date}, 过期日期: ${batch.expiry_date}`, 'reset');
        });
    }

    if (testResults.inventory.length > 0) {
        log('库存信息:', 'yellow');
        testResults.inventory.forEach(inv => {
            log(`  - 单品ID: ${inv.item_id}, 当前库存: ${inv.current_quantity}, 总入库: ${inv.total_in}, 总出库: ${inv.total_out}`, 'reset');
        });
    }

    if (testResults.alerts.length > 0) {
        log('预警信息:', 'yellow');
        testResults.alerts.forEach(alert => {
            const status = alert.is_triggered ? '🔴 已触发' : '🟢 正常';
            log(`  - 单品ID: ${alert.item_id}, 阈值: ${alert.threshold}, 当前库存: ${alert.current_stock}, 状态: ${status}`, 'reset');
        });
    }
}

// 提供使用建议
function provideSuggestions() {
    logHeader('使用建议');

    log('为了完整测试应用，建议执行以下步骤:', 'bright');
    log('1. 启动Tauri开发服务器:', 'cyan');
    log('   bun run tauri dev', 'reset');

    log('\n2. 在浏览器中打开调试页面:', 'cyan');
    log('   http://localhost:1420/debug-frontend.html', 'reset');

    log('\n3. 运行后端单元测试:', 'cyan');
    log('   cd src-tauri && cargo test -- --nocapture', 'reset');

    log('\n4. 检查TypeScript编译:', 'cyan');
    log('   npx tsc --noEmit', 'reset');

    log('\n5. 检查前端构建:', 'cyan');
    log('   bun run build', 'reset');

    log('\n📝 注意事项:', 'yellow');
    log('- 确保所有依赖都已正确安装', 'reset');
    log('- 数据库会在首次运行时自动创建和迁移', 'reset');
    log('- 如果遇到端口占用，请先杀掉相关进程', 'reset');
    log('- 建议在Tauri应用中测试前端API连接', 'reset');
}

// 主函数
function main() {
    log('🚀 FishTouch Store API 终端测试工具', 'bright');
    log('='.repeat(60), 'cyan');

    const checks = [
        checkProjectStructure(),
        checkDependencies()
    ];

    checkDatabase();
    simulateApiTests();
    generateTestReport();
    provideSuggestions();

    const allPassed = checks.every(check => check);

    log('\n' + '='.repeat(60), 'cyan');
    if (allPassed) {
        logSuccess('所有基础检查都通过了！项目结构看起来正常。');
        logInfo('请按照上述建议进行完整的功能测试。');
    } else {
        logError('部分检查失败，请修复相关问题后重新测试。');
    }
    log('='.repeat(60), 'cyan');
}

// 运行测试
main();
