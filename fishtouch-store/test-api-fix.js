#!/usr/bin/env node

/**
 * API修复验证脚本
 * 测试前端API初始化和调用是否正常工作
 */

import fs from 'fs';
import path from 'path';

// 颜色输出
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查API文件的修复
function checkApiFileChanges() {
    log('\n🔧 检查API文件修复', 'cyan');
    log('='.repeat(50), 'cyan');

    try {
        const apiFilePath = path.join(process.cwd(), 'src', 'services', 'api.ts');
        const apiContent = fs.readFileSync(apiFilePath, 'utf8');

        // 检查关键修复点
        const checks = [
            {
                name: '异步API初始化',
                pattern: /ensureApiInitialized/,
                description: '确保API初始化函数存在'
            },
            {
                name: 'Promise-based apiInvoke',
                pattern: /const apiInvoke = async \(command: string, args\?: any\): Promise<any>/,
                description: '确保apiInvoke是异步函数'
            },
            {
                name: '等待初始化',
                pattern: /await ensureApiInitialized\(\)/,
                description: '确保在调用前等待初始化'
            },
            {
                name: 'Tauri环境检测',
                pattern: /window\.__TAURI__/,
                description: '确保正确检测Tauri环境'
            },
            {
                name: '日志输出',
                pattern: /console\.log.*API call/,
                description: '确保有调试日志'
            }
        ];

        let passedChecks = 0;

        checks.forEach(check => {
            if (check.pattern.test(apiContent)) {
                log(`✅ ${check.name}: ${check.description}`, 'green');
                passedChecks++;
            } else {
                log(`❌ ${check.name}: ${check.description}`, 'red');
            }
        });

        log(`\n📊 检查结果: ${passedChecks}/${checks.length} 项通过`, passedChecks === checks.length ? 'green' : 'yellow');

        return passedChecks === checks.length;

    } catch (error) {
        log(`❌ 检查API文件失败: ${error.message}`, 'red');
        return false;
    }
}

// 检查TypeScript编译
async function checkTypeScriptCompilation() {
    log('\n🔧 检查TypeScript编译', 'cyan');
    log('='.repeat(50), 'cyan');

    return new Promise(async (resolve) => {
        const { spawn } = await import('child_process');
        const tsc = spawn('npx', ['tsc', '--noEmit'], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let output = '';
        let errorOutput = '';

        tsc.stdout.on('data', (data) => {
            output += data.toString();
        });

        tsc.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        tsc.on('close', (code) => {
            if (code === 0) {
                log('✅ TypeScript编译通过', 'green');
                resolve(true);
            } else {
                log('❌ TypeScript编译失败', 'red');
                if (errorOutput) {
                    log(`错误信息: ${errorOutput}`, 'red');
                }
                resolve(false);
            }
        });
    });
}

// 检查数据库状态
function checkDatabaseStatus() {
    log('\n🔧 检查数据库状态', 'cyan');
    log('='.repeat(50), 'cyan');

    try {
        const dbPath = path.join(process.cwd(), 'src-tauri', 'database.db');

        if (fs.existsSync(dbPath)) {
            const stats = fs.statSync(dbPath);
            log(`✅ 数据库文件存在: ${dbPath}`, 'green');
            log(`📊 文件大小: ${(stats.size / 1024).toFixed(2)} KB`, 'blue');
            log(`📅 最后修改: ${stats.mtime.toLocaleString()}`, 'blue');
            return true;
        } else {
            log(`❌ 数据库文件不存在: ${dbPath}`, 'red');
            return false;
        }
    } catch (error) {
        log(`❌ 检查数据库失败: ${error.message}`, 'red');
        return false;
    }
}

// 检查Tauri配置
function checkTauriConfig() {
    log('\n🔧 检查Tauri配置', 'cyan');
    log('='.repeat(50), 'cyan');

    try {
        const configPath = path.join(process.cwd(), 'src-tauri', 'tauri.conf.json');

        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

            log('✅ Tauri配置文件存在', 'green');

            // 检查关键配置
            if (config.app && config.app.devUrl) {
                log(`✅ 开发服务器URL: ${config.app.devUrl}`, 'green');
            }

            if (config.bundle && config.bundle.identifier) {
                log(`✅ 应用标识符: ${config.bundle.identifier}`, 'green');
            }

            return true;
        } else {
            log(`❌ Tauri配置文件不存在: ${configPath}`, 'red');
            return false;
        }
    } catch (error) {
        log(`❌ 检查Tauri配置失败: ${error.message}`, 'red');
        return false;
    }
}

// 生成修复报告
function generateFixReport(results) {
    log('\n📋 修复验证报告', 'magenta');
    log('='.repeat(60), 'magenta');

    const totalChecks = Object.keys(results).length;
    const passedChecks = Object.values(results).filter(Boolean).length;

    log(`\n📊 总体结果: ${passedChecks}/${totalChecks} 项检查通过`, passedChecks === totalChecks ? 'green' : 'yellow');

    Object.entries(results).forEach(([check, passed]) => {
        const status = passed ? '✅ 通过' : '❌ 失败';
        const color = passed ? 'green' : 'red';
        log(`${status} ${check}`, color);
    });

    if (passedChecks === totalChecks) {
        log('\n🎉 所有检查都通过了！API修复应该已经生效。', 'green');
        log('\n💡 建议测试步骤:', 'blue');
        log('1. 在Tauri应用中打开 http://localhost:1420/test-data-persistence.html', 'blue');
        log('2. 点击"创建测试套餐"和"创建测试单品"', 'blue');
        log('3. 验证数据是否正确显示和保存', 'blue');
        log('4. 刷新页面后再次获取数据，确认数据持久化', 'blue');
    } else {
        log('\n⚠️  部分检查未通过，可能需要进一步调试。', 'yellow');
    }

    log('\n🔗 相关文件:', 'cyan');
    log('- API服务: src/services/api.ts', 'cyan');
    log('- 测试页面: test-data-persistence.html', 'cyan');
    log('- 数据库: src-tauri/database.db', 'cyan');
}

// 主函数
async function main() {
    log('🚀 FishTouch Store API修复验证工具', 'bright');
    log('='.repeat(60), 'bright');

    const results = {
        'API文件修复': checkApiFileChanges(),
        'TypeScript编译': await checkTypeScriptCompilation(),
        '数据库状态': checkDatabaseStatus(),
        'Tauri配置': checkTauriConfig()
    };

    generateFixReport(results);
}

// 运行验证
main().catch(error => {
    log(`\n❌ 验证过程出错: ${error.message}`, 'red');
    process.exit(1);
});
