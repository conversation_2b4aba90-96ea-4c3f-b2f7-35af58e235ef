<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据持久化测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .data-list {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        .data-item {
            padding: 5px;
            margin: 2px 0;
            background: white;
            border-radius: 3px;
            border-left: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 FishTouch Store 数据持久化测试</h1>
        <p class="info">此页面用于测试套餐和单品的创建、保存和获取功能，确保数据不会"消失"。</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>🔧 环境检查</h3>
            <button onclick="checkEnvironment()">检查Tauri环境</button>
            <div id="env-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📦 套餐测试</h3>
            <button onclick="createTestPackage()">创建测试套餐</button>
            <button onclick="getAllPackages()">获取所有套餐</button>
            <button onclick="clearPackageResults()">清空结果</button>
            <div id="package-result" class="result" style="display: none;"></div>
            <div id="package-list" class="data-list" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🍔 单品测试</h3>
            <button onclick="createTestItem()">创建测试单品</button>
            <button onclick="getAllItems()">获取所有单品</button>
            <button onclick="clearItemResults()">清空结果</button>
            <div id="item-result" class="result" style="display: none;"></div>
            <div id="item-list" class="data-list" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 完整流程测试</h3>
            <button onclick="runFullTest()">运行完整测试</button>
            <div id="full-test-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 检查Tauri环境
        async function checkEnvironment() {
            const resultDiv = document.getElementById('env-result');
            resultDiv.style.display = 'block';
            
            try {
                if (window.__TAURI__) {
                    resultDiv.innerHTML = '<span class="success">✅ Tauri环境检测成功</span>';
                    
                    // 测试API调用
                    const { invoke } = await import("@tauri-apps/api/core");
                    const packages = await invoke("get_all_packages");
                    resultDiv.innerHTML += '<br><span class="success">✅ Tauri API调用成功</span>';
                    resultDiv.innerHTML += `<br><span class="info">当前数据库中有 ${packages.length} 个套餐</span>`;
                } else {
                    resultDiv.innerHTML = '<span class="error">❌ 不在Tauri环境中</span>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 环境检查失败: ${error.message}</span>`;
            }
        }

        // 创建测试套餐
        async function createTestPackage() {
            const resultDiv = document.getElementById('package-result');
            resultDiv.style.display = 'block';
            
            try {
                const { invoke } = await import("@tauri-apps/api/core");
                const timestamp = Date.now();
                const packageData = {
                    name: `测试套餐_${timestamp}`,
                    description: `这是一个测试套餐，创建时间: ${new Date().toLocaleString()}`
                };
                
                resultDiv.innerHTML = '<span class="info">⏳ 正在创建套餐...</span>';
                
                const newPackage = await invoke("create_package", packageData);
                
                resultDiv.innerHTML = `<span class="success">✅ 套餐创建成功!</span><br>
                    ID: ${newPackage.id}<br>
                    名称: ${newPackage.name}<br>
                    描述: ${newPackage.description || '无'}`;
                
                // 自动刷新套餐列表
                setTimeout(() => getAllPackages(), 500);
                
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 创建套餐失败: ${error.message}</span>`;
            }
        }

        // 获取所有套餐
        async function getAllPackages() {
            const resultDiv = document.getElementById('package-result');
            const listDiv = document.getElementById('package-list');
            
            try {
                const { invoke } = await import("@tauri-apps/api/core");
                
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<span class="info">⏳ 正在获取套餐列表...</span>';
                
                const packages = await invoke("get_all_packages");
                
                resultDiv.innerHTML = `<span class="success">✅ 获取到 ${packages.length} 个套餐</span>`;
                
                if (packages.length > 0) {
                    listDiv.style.display = 'block';
                    listDiv.innerHTML = packages.map(pkg => 
                        `<div class="data-item">
                            <strong>ID: ${pkg.id}</strong> - ${pkg.name}
                            ${pkg.description ? `<br><small>${pkg.description}</small>` : ''}
                        </div>`
                    ).join('');
                } else {
                    listDiv.style.display = 'block';
                    listDiv.innerHTML = '<div class="data-item">暂无套餐数据</div>';
                }
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<span class="error">❌ 获取套餐失败: ${error.message}</span>`;
            }
        }

        // 创建测试单品
        async function createTestItem() {
            const resultDiv = document.getElementById('item-result');
            resultDiv.style.display = 'block';
            
            try {
                const { invoke } = await import("@tauri-apps/api/core");
                const timestamp = Date.now();
                const itemData = {
                    name: `测试单品_${timestamp}`,
                    sku: `TEST_${timestamp}`
                };
                
                resultDiv.innerHTML = '<span class="info">⏳ 正在创建单品...</span>';
                
                const newItem = await invoke("create_item", itemData);
                
                resultDiv.innerHTML = `<span class="success">✅ 单品创建成功!</span><br>
                    ID: ${newItem.id}<br>
                    名称: ${newItem.name}<br>
                    SKU: ${newItem.sku}`;
                
                // 自动刷新单品列表
                setTimeout(() => getAllItems(), 500);
                
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 创建单品失败: ${error.message}</span>`;
            }
        }

        // 获取所有单品
        async function getAllItems() {
            const resultDiv = document.getElementById('item-result');
            const listDiv = document.getElementById('item-list');
            
            try {
                const { invoke } = await import("@tauri-apps/api/core");
                
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = '<span class="info">⏳ 正在获取单品列表...</span>';
                
                const items = await invoke("get_all_items");
                
                resultDiv.innerHTML = `<span class="success">✅ 获取到 ${items.length} 个单品</span>`;
                
                if (items.length > 0) {
                    listDiv.style.display = 'block';
                    listDiv.innerHTML = items.map(item => 
                        `<div class="data-item">
                            <strong>ID: ${item.id}</strong> - ${item.name}
                            <br><small>SKU: ${item.sku}</small>
                        </div>`
                    ).join('');
                } else {
                    listDiv.style.display = 'block';
                    listDiv.innerHTML = '<div class="data-item">暂无单品数据</div>';
                }
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `<span class="error">❌ 获取单品失败: ${error.message}</span>`;
            }
        }

        // 清空结果
        function clearPackageResults() {
            document.getElementById('package-result').style.display = 'none';
            document.getElementById('package-list').style.display = 'none';
        }

        function clearItemResults() {
            document.getElementById('item-result').style.display = 'none';
            document.getElementById('item-list').style.display = 'none';
        }

        // 运行完整测试
        async function runFullTest() {
            const resultDiv = document.getElementById('full-test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<span class="info">⏳ 开始完整测试...</span>';
            
            try {
                // 1. 环境检查
                resultDiv.innerHTML += '<br>1. 检查环境...';
                await checkEnvironment();
                
                // 2. 创建套餐
                resultDiv.innerHTML += '<br>2. 创建测试套餐...';
                await createTestPackage();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 3. 创建单品
                resultDiv.innerHTML += '<br>3. 创建测试单品...';
                await createTestItem();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 4. 验证数据持久化
                resultDiv.innerHTML += '<br>4. 验证数据持久化...';
                await getAllPackages();
                await getAllItems();
                
                resultDiv.innerHTML += '<br><span class="success">✅ 完整测试完成！</span>';
                
            } catch (error) {
                resultDiv.innerHTML += `<br><span class="error">❌ 测试失败: ${error.message}</span>`;
            }
        }

        // 页面加载时自动检查环境
        window.addEventListener('load', () => {
            setTimeout(checkEnvironment, 1000);
        });
    </script>
</body>
</html>
