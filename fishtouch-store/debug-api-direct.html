<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接API调试</title>
    <style>
        body {
            font-family: monospace;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #1e1e1e;
            color: #fff;
        }
        .section {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-family: monospace;
        }
        button:hover {
            background: #005a9e;
        }
        .result {
            background: #000;
            border: 1px solid #444;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .info { color: #2196F3; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="section">
        <h1>🔧 直接API调试工具</h1>
        <p>直接测试Tauri后端API调用，绕过前端服务层</p>
    </div>

    <div class="section">
        <h3>环境检查</h3>
        <button onclick="checkTauriEnv()">检查Tauri环境</button>
        <div id="env-result" class="result"></div>
    </div>

    <div class="section">
        <h3>套餐API测试</h3>
        <button onclick="testGetAllPackages()">获取所有套餐</button>
        <button onclick="testCreatePackage()">创建测试套餐</button>
        <div id="package-result" class="result"></div>
    </div>

    <div class="section">
        <h3>单品API测试</h3>
        <button onclick="testGetAllItems()">获取所有单品</button>
        <button onclick="testCreateItem()">创建测试单品</button>
        <div id="item-result" class="result"></div>
    </div>

    <div class="section">
        <h3>数据库直接查询</h3>
        <button onclick="testDatabaseQuery()">查询数据库状态</button>
        <div id="db-result" class="result"></div>
    </div>

    <script>
        let invoke = null;

        // 检查Tauri环境
        async function checkTauriEnv() {
            const result = document.getElementById('env-result');
            result.innerHTML = '';
            
            try {
                if (window.__TAURI__) {
                    result.innerHTML += '<span class="success">✅ Tauri环境检测成功</span>\n';
                    
                    // 导入Tauri API
                    const tauriApi = await import("@tauri-apps/api/core");
                    invoke = tauriApi.invoke;
                    result.innerHTML += '<span class="success">✅ Tauri API导入成功</span>\n';
                    
                    // 测试基本调用
                    result.innerHTML += '<span class="info">🔍 测试基本API调用...</span>\n';
                    
                } else {
                    result.innerHTML += '<span class="error">❌ 不在Tauri环境中</span>\n';
                    result.innerHTML += '<span class="warning">⚠️  请在Tauri应用中打开此页面</span>\n';
                }
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 环境检查失败: ${error.message}</span>\n`;
                console.error('环境检查错误:', error);
            }
        }

        // 测试获取所有套餐
        async function testGetAllPackages() {
            const result = document.getElementById('package-result');
            result.innerHTML = '';
            
            if (!invoke) {
                result.innerHTML = '<span class="error">❌ Tauri API未初始化，请先检查环境</span>\n';
                return;
            }
            
            try {
                result.innerHTML += '<span class="info">🔍 调用 get_all_packages...</span>\n';
                
                const packages = await invoke("get_all_packages");
                
                result.innerHTML += `<span class="success">✅ API调用成功</span>\n`;
                result.innerHTML += `<span class="info">📊 返回数据类型: ${typeof packages}</span>\n`;
                result.innerHTML += `<span class="info">📊 数据长度: ${Array.isArray(packages) ? packages.length : 'N/A'}</span>\n`;
                result.innerHTML += `<span class="info">📋 原始数据:</span>\n`;
                result.innerHTML += JSON.stringify(packages, null, 2) + '\n';
                
                if (Array.isArray(packages) && packages.length > 0) {
                    result.innerHTML += `<span class="success">✅ 找到 ${packages.length} 个套餐</span>\n`;
                    packages.forEach((pkg, index) => {
                        result.innerHTML += `<span class="info">  ${index + 1}. ID:${pkg.id} 名称:${pkg.name}</span>\n`;
                    });
                } else {
                    result.innerHTML += `<span class="warning">⚠️  没有找到套餐数据</span>\n`;
                }
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ API调用失败: ${error.message}</span>\n`;
                console.error('获取套餐错误:', error);
            }
        }

        // 测试创建套餐
        async function testCreatePackage() {
            const result = document.getElementById('package-result');
            
            if (!invoke) {
                result.innerHTML = '<span class="error">❌ Tauri API未初始化，请先检查环境</span>\n';
                return;
            }
            
            try {
                const timestamp = Date.now();
                const packageData = {
                    name: `调试套餐_${timestamp}`,
                    description: `调试创建的套餐 ${new Date().toLocaleString()}`
                };
                
                result.innerHTML += `<span class="info">🔍 创建套餐: ${packageData.name}</span>\n`;
                
                const newPackage = await invoke("create_package", packageData);
                
                result.innerHTML += `<span class="success">✅ 套餐创建成功</span>\n`;
                result.innerHTML += `<span class="info">📋 新套餐数据:</span>\n`;
                result.innerHTML += JSON.stringify(newPackage, null, 2) + '\n';
                
                // 立即重新获取列表验证
                setTimeout(testGetAllPackages, 500);
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 创建套餐失败: ${error.message}</span>\n`;
                console.error('创建套餐错误:', error);
            }
        }

        // 测试获取所有单品
        async function testGetAllItems() {
            const result = document.getElementById('item-result');
            result.innerHTML = '';
            
            if (!invoke) {
                result.innerHTML = '<span class="error">❌ Tauri API未初始化，请先检查环境</span>\n';
                return;
            }
            
            try {
                result.innerHTML += '<span class="info">🔍 调用 get_all_items...</span>\n';
                
                const items = await invoke("get_all_items");
                
                result.innerHTML += `<span class="success">✅ API调用成功</span>\n`;
                result.innerHTML += `<span class="info">📊 返回数据类型: ${typeof items}</span>\n`;
                result.innerHTML += `<span class="info">📊 数据长度: ${Array.isArray(items) ? items.length : 'N/A'}</span>\n`;
                result.innerHTML += `<span class="info">📋 原始数据:</span>\n`;
                result.innerHTML += JSON.stringify(items, null, 2) + '\n';
                
                if (Array.isArray(items) && items.length > 0) {
                    result.innerHTML += `<span class="success">✅ 找到 ${items.length} 个单品</span>\n`;
                    items.forEach((item, index) => {
                        result.innerHTML += `<span class="info">  ${index + 1}. ID:${item.id} 名称:${item.name} SKU:${item.sku}</span>\n`;
                    });
                } else {
                    result.innerHTML += `<span class="warning">⚠️  没有找到单品数据</span>\n`;
                }
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ API调用失败: ${error.message}</span>\n`;
                console.error('获取单品错误:', error);
            }
        }

        // 测试创建单品
        async function testCreateItem() {
            const result = document.getElementById('item-result');
            
            if (!invoke) {
                result.innerHTML = '<span class="error">❌ Tauri API未初始化，请先检查环境</span>\n';
                return;
            }
            
            try {
                const timestamp = Date.now();
                const itemData = {
                    name: `调试单品_${timestamp}`,
                    sku: `DEBUG_${timestamp}`
                };
                
                result.innerHTML += `<span class="info">🔍 创建单品: ${itemData.name}</span>\n`;
                
                const newItem = await invoke("create_item", itemData);
                
                result.innerHTML += `<span class="success">✅ 单品创建成功</span>\n`;
                result.innerHTML += `<span class="info">📋 新单品数据:</span>\n`;
                result.innerHTML += JSON.stringify(newItem, null, 2) + '\n';
                
                // 立即重新获取列表验证
                setTimeout(testGetAllItems, 500);
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 创建单品失败: ${error.message}</span>\n`;
                console.error('创建单品错误:', error);
            }
        }

        // 测试数据库查询
        async function testDatabaseQuery() {
            const result = document.getElementById('db-result');
            result.innerHTML = '';
            
            if (!invoke) {
                result.innerHTML = '<span class="error">❌ Tauri API未初始化，请先检查环境</span>\n';
                return;
            }
            
            try {
                result.innerHTML += '<span class="info">🔍 查询数据库状态...</span>\n';
                
                // 同时获取套餐和单品
                const [packages, items] = await Promise.all([
                    invoke("get_all_packages"),
                    invoke("get_all_items")
                ]);
                
                result.innerHTML += `<span class="success">✅ 数据库查询成功</span>\n`;
                result.innerHTML += `<span class="info">📊 套餐数量: ${packages.length}</span>\n`;
                result.innerHTML += `<span class="info">📊 单品数量: ${items.length}</span>\n`;
                
                if (packages.length === 0 && items.length === 0) {
                    result.innerHTML += `<span class="warning">⚠️  数据库中没有数据，这可能是问题所在</span>\n`;
                } else {
                    result.innerHTML += `<span class="success">✅ 数据库中有数据，前端显示可能有问题</span>\n`;
                }
                
            } catch (error) {
                result.innerHTML += `<span class="error">❌ 数据库查询失败: ${error.message}</span>\n`;
                console.error('数据库查询错误:', error);
            }
        }

        // 页面加载时自动检查环境
        window.addEventListener('load', () => {
            setTimeout(checkTauriEnv, 1000);
        });
    </script>
</body>
</html>
