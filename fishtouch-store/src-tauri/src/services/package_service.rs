use diesel::prelude::*;
use crate::database::DbConnection;
use crate::models::{Package, NewPackage};
use crate::schema::packages;

pub struct PackageService;

impl PackageService {
    pub fn create(conn: &mut DbConnection, new_package: NewPackage) -> QueryResult<Package> {
        diesel::insert_into(packages::table)
            .values(&new_package)
            .execute(conn)?;

        // Get the last inserted row
        packages::table
            .order(packages::id.desc())
            .select(Package::as_select())
            .first(conn)
    }

    pub fn get_all(conn: &mut DbConnection) -> QueryResult<Vec<Package>> {
        packages::table.select(Package::as_select()).load(conn)
    }

    pub fn get_by_id(conn: &mut DbConnection, package_id: i32) -> QueryResult<Package> {
        packages::table
            .filter(packages::id.eq(package_id))
            .select(Package::as_select())
            .first(conn)
    }

    pub fn update(
        conn: &mut DbConnection,
        package_id: i32,
        name: Option<String>,
        description: Option<Option<String>>,
    ) -> QueryResult<Package> {
        // Build the changeset based on what's provided
        match (&name, &description) {
            (Some(name_val), Some(desc_val)) => {
                // Update both name and description
                diesel::update(packages::table.filter(packages::id.eq(package_id)))
                    .set((
                        packages::name.eq(name_val),
                        packages::description.eq(desc_val),
                    ))
                    .execute(conn)?;
            }
            (Some(name_val), None) => {
                // Update only name
                diesel::update(packages::table.filter(packages::id.eq(package_id)))
                    .set(packages::name.eq(name_val))
                    .execute(conn)?;
            }
            (None, Some(desc_val)) => {
                // Update only description
                diesel::update(packages::table.filter(packages::id.eq(package_id)))
                    .set(packages::description.eq(desc_val))
                    .execute(conn)?;
            }
            (None, None) => {
                // Nothing to update
            }
        }

        // Return the updated package
        Self::get_by_id(conn, package_id)
    }

    pub fn delete(conn: &mut DbConnection, package_id: i32) -> QueryResult<usize> {
        diesel::delete(packages::table.filter(packages::id.eq(package_id))).execute(conn)
    }

    pub fn search_by_name(conn: &mut DbConnection, search_term: &str) -> QueryResult<Vec<Package>> {
        packages::table
            .filter(packages::name.like(format!("%{}%", search_term)))
            .select(Package::as_select())
            .load(conn)
    }
}
