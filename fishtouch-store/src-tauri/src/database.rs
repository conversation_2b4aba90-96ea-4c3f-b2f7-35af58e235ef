use diesel::prelude::*;
use diesel::sqlite::SqliteConnection;
use std::sync::{Arc, Mutex};
use std::env;

pub type DbConnection = SqliteConnection;
pub type DbPool = Arc<Mutex<DbConnection>>;

pub fn establish_connection() -> DbConnection {
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "database.db".to_string());

    println!("Current working directory: {:?}", env::current_dir());
    println!("Connecting to database: {}", database_url);

    let connection = SqliteConnection::establish(&database_url)
        .unwrap_or_else(|e| panic!("Error connecting to {}: {}", database_url, e));

    println!("Database connection established successfully");
    connection
}

pub fn create_pool() -> DbPool {
    let connection = establish_connection();
    Arc::new(Mutex::new(connection))
}

// Helper function to run migrations programmatically
pub fn run_migrations(connection: &mut SqliteConnection) -> Result<(), Box<dyn std::error::Error + Send + Sync + 'static>> {
    use diesel_migrations::{embed_migrations, EmbeddedMigrations, MigrationHarness};

    println!("Loading embedded migrations...");
    const MIGRATIONS: EmbeddedMigrations = embed_migrations!();

    println!("Running pending migrations...");
    let migration_result = connection.run_pending_migrations(MIGRATIONS);

    match &migration_result {
        Ok(versions) => {
            println!("Successfully ran {} migrations: {:?}", versions.len(), versions);
        }
        Err(e) => {
            println!("Migration error: {}", e);
        }
    }

    migration_result?;
    Ok(())
}

#[cfg(test)]
pub fn establish_test_connection() -> DbConnection {
    let database_url = ":memory:";
    let mut connection = SqliteConnection::establish(database_url)
        .unwrap_or_else(|_| panic!("Error connecting to test database"));
    
    // Run migrations for test database
    run_migrations(&mut connection).expect("Failed to run migrations");
    connection
}
