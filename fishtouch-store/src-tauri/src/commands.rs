use tauri::State;
use std::sync::{Arc, Mutex};
use chrono::{NaiveDate, NaiveDateTime};

use crate::database::DbConnection;
use crate::models::*;
use crate::services::*;

pub type AppState = Arc<Mutex<DbConnection>>;

// Package Commands
#[tauri::command]
pub fn create_package(
    state: State<AppState>,
    name: String,
    description: Option<String>,
) -> Result<Package, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    let new_package = NewPackage { name, description };
    
    PackageService::create(&mut *conn, new_package)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_all_packages(state: State<AppState>) -> Result<Vec<Package>, String> {
    println!("get_all_packages called - testing");
    let mut conn = state.lock().map_err(|e| {
        let error_msg = format!("Failed to acquire database lock: {}", e);
        println!("{}", error_msg);
        error_msg
    })?;

    let result = PackageService::get_all(&mut *conn).map_err(|e| {
        let error_msg = format!("Database error getting packages: {}", e);
        println!("{}", error_msg);
        error_msg
    });

    match &result {
        Ok(packages) => println!("Successfully retrieved {} packages", packages.len()),
        Err(e) => println!("Error retrieving packages: {}", e),
    }

    result
}

#[tauri::command]
pub fn get_package_by_id(state: State<AppState>, id: i32) -> Result<Package, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    PackageService::get_by_id(&mut *conn, id).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn update_package(
    state: State<AppState>,
    id: i32,
    name: String,
    description: Option<String>,
) -> Result<Package, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    PackageService::update(&mut *conn, id, Some(name), Some(description))
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn delete_package(state: State<AppState>, id: i32) -> Result<usize, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    PackageService::delete(&mut *conn, id).map_err(|e| e.to_string())
}

// Item Commands
#[tauri::command]
pub fn create_item(
    state: State<AppState>,
    name: String,
    sku: String,
) -> Result<Item, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    let new_item = NewItem { name, sku };
    
    ItemService::create(&mut *conn, new_item)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_all_items(state: State<AppState>) -> Result<Vec<Item>, String> {
    println!("get_all_items called - testing");
    let mut conn = state.lock().map_err(|e| {
        let error_msg = format!("Failed to acquire database lock: {}", e);
        println!("{}", error_msg);
        error_msg
    })?;

    let result = ItemService::get_all(&mut *conn).map_err(|e| {
        let error_msg = format!("Database error getting items: {}", e);
        println!("{}", error_msg);
        error_msg
    });

    match &result {
        Ok(items) => println!("Successfully retrieved {} items", items.len()),
        Err(e) => println!("Error retrieving items: {}", e),
    }

    result
}

#[tauri::command]
pub fn get_item_by_id(state: State<AppState>, id: i32) -> Result<Item, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::get_by_id(&mut *conn, id).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_item_by_sku(state: State<AppState>, sku: String) -> Result<Item, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::get_by_sku(&mut *conn, &sku).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn update_item(
    state: State<AppState>,
    id: i32,
    name: Option<String>,
    sku: Option<String>,
) -> Result<Item, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::update(&mut *conn, id, name, sku).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn delete_item(state: State<AppState>, id: i32) -> Result<usize, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::delete(&mut *conn, id).map_err(|e| e.to_string())
}

// Recipe Commands
#[tauri::command]
pub fn create_recipe(
    state: State<AppState>,
    package_id: i32,
    item_id: i32,
    quantity: f32,
    unit: String,
    valid_from: String, // ISO date string
    valid_to: Option<String>, // ISO date string
) -> Result<Recipe, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    
    let valid_from = NaiveDate::parse_from_str(&valid_from, "%Y-%m-%d")
        .map_err(|e| format!("Invalid valid_from date: {}", e))?;
    
    let valid_to = if let Some(date_str) = valid_to {
        Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid valid_to date: {}", e))?)
    } else {
        None
    };
    
    let new_recipe = NewRecipe {
        package_id,
        item_id,
        quantity,
        unit,
        valid_from,
        valid_to,
    };
    
    RecipeService::create(&mut *conn, new_recipe)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_recipes_by_package_id(state: State<AppState>, package_id: i32) -> Result<Vec<Recipe>, String> {
    println!("Getting recipes for package_id: {}", package_id);
    let mut conn = state.lock().map_err(|e| {
        let error_msg = format!("Failed to acquire database lock: {}", e);
        println!("{}", error_msg);
        error_msg
    })?;

    let result = RecipeService::get_by_package_id(&mut *conn, package_id).map_err(|e| {
        let error_msg = format!("Database error getting recipes for package {}: {}", package_id, e);
        println!("{}", error_msg);
        error_msg
    });

    match &result {
        Ok(recipes) => println!("Successfully retrieved {} recipes for package {}", recipes.len(), package_id),
        Err(e) => println!("Error retrieving recipes: {}", e),
    }

    result
}

#[tauri::command]
pub fn get_all_recipes(state: State<AppState>) -> Result<Vec<Recipe>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    RecipeService::get_all(&mut *conn).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_recipe_by_id(state: State<AppState>, id: i32) -> Result<Recipe, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    RecipeService::get_by_id(&mut *conn, id).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn update_recipe(
    state: State<AppState>,
    id: i32,
    quantity: Option<f32>,
    unit: Option<String>,
    valid_from: Option<String>, // ISO date string
    valid_to: Option<String>, // ISO date string
) -> Result<Recipe, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let valid_from_date = if let Some(date_str) = valid_from {
        Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid valid_from date: {}", e))?)
    } else {
        None
    };

    let valid_to_date = if let Some(date_str) = valid_to {
        Some(Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid valid_to date: {}", e))?))
    } else {
        None
    };

    RecipeService::update(&mut *conn, id, quantity, unit, valid_from_date, valid_to_date)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn delete_recipe(state: State<AppState>, id: i32) -> Result<usize, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    RecipeService::delete(&mut *conn, id).map_err(|e| e.to_string())
}

// Batch Commands
#[tauri::command]
pub fn create_batch(
    state: State<AppState>,
    item_id: i32,
    batch_number: String,
    in_date: String, // ISO date string
    expiry_date: Option<String>, // ISO date string
) -> Result<Batch, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    
    let in_date = NaiveDate::parse_from_str(&in_date, "%Y-%m-%d")
        .map_err(|e| format!("Invalid in_date: {}", e))?;
    
    let expiry_date = if let Some(date_str) = expiry_date {
        Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid expiry_date: {}", e))?)
    } else {
        None
    };
    
    let new_batch = NewBatch {
        item_id,
        batch_number,
        in_date,
        expiry_date,
    };
    
    BatchService::create(&mut *conn, new_batch)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_batches_by_item_id(state: State<AppState>, item_id: i32) -> Result<Vec<Batch>, String> {
    println!("Getting batches for item_id: {}", item_id);
    let mut conn = state.lock().map_err(|e| {
        let error_msg = format!("Failed to acquire database lock: {}", e);
        println!("{}", error_msg);
        error_msg
    })?;

    let result = BatchService::get_by_item_id(&mut *conn, item_id).map_err(|e| {
        let error_msg = format!("Database error getting batches for item {}: {}", item_id, e);
        println!("{}", error_msg);
        error_msg
    });

    match &result {
        Ok(batches) => println!("Successfully retrieved {} batches for item {}", batches.len(), item_id),
        Err(e) => println!("Error retrieving batches: {}", e),
    }

    result
}

#[tauri::command]
pub fn get_all_batches(state: State<AppState>) -> Result<Vec<Batch>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    BatchService::get_all(&mut *conn).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_batch_by_id(state: State<AppState>, id: i32) -> Result<Batch, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    BatchService::get_by_id(&mut *conn, id).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn update_batch(
    state: State<AppState>,
    id: i32,
    batch_number: Option<String>,
    in_date: Option<String>, // ISO date string
    expiry_date: Option<String>, // ISO date string
) -> Result<Batch, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let in_date_parsed = if let Some(date_str) = in_date {
        Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid in_date: {}", e))?)
    } else {
        None
    };

    let expiry_date_parsed = if let Some(date_str) = expiry_date {
        Some(Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid expiry_date: {}", e))?))
    } else {
        None
    };

    BatchService::update(&mut *conn, id, batch_number, in_date_parsed, expiry_date_parsed)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn delete_batch(state: State<AppState>, id: i32) -> Result<usize, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    BatchService::delete(&mut *conn, id).map_err(|e| e.to_string())
}

// Inventory Commands
#[tauri::command]
pub fn create_purchase_order(
    state: State<AppState>,
    order_number: String,
    order_date: String, // ISO datetime string
    supplier: String,
) -> Result<PurchaseOrder, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let order_date = NaiveDateTime::parse_from_str(&order_date, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid order_date: {}", e))?;

    let new_order = NewPurchaseOrder {
        order_number,
        order_date,
        supplier,
    };

    InventoryService::create_purchase_order(&mut *conn, new_order)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn create_shipment_order(
    state: State<AppState>,
    order_number: String,
    order_date: String, // ISO datetime string
    customer: String,
) -> Result<ShipmentOrder, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let order_date = NaiveDateTime::parse_from_str(&order_date, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid order_date: {}", e))?;

    let new_order = NewShipmentOrder {
        order_number,
        order_date,
        customer,
    };

    InventoryService::create_shipment_order(&mut *conn, new_order)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn record_purchase_transaction(
    state: State<AppState>,
    batch_id: i32,
    quantity: i32,
    order_number: String,
    timestamp: String, // ISO datetime string
) -> Result<InventoryTransaction, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let timestamp = NaiveDateTime::parse_from_str(&timestamp, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid timestamp: {}", e))?;

    InventoryService::record_purchase_transaction(&mut *conn, batch_id, quantity, order_number, timestamp)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn record_shipment_transaction(
    state: State<AppState>,
    batch_id: i32,
    quantity: i32,
    order_number: String,
    timestamp: String, // ISO datetime string
) -> Result<InventoryTransaction, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let timestamp = NaiveDateTime::parse_from_str(&timestamp, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid timestamp: {}", e))?;

    InventoryService::record_shipment_transaction(&mut *conn, batch_id, quantity, order_number, timestamp)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_current_stock_for_item(state: State<AppState>, item_id: i32) -> Result<StockLevel, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    InventoryService::get_current_stock_for_item(&mut *conn, item_id)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_all_stock_levels(state: State<AppState>) -> Result<Vec<StockLevel>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    InventoryService::get_all_stock_levels(&mut *conn)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn check_low_stock_alerts(state: State<AppState>) -> Result<Vec<(Item, InventoryAlert, i32)>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    InventoryService::check_low_stock_alerts(&mut *conn)
        .map_err(|e| e.to_string())
}

// Test Data Command
#[tauri::command]
pub fn create_sample_data(state: State<AppState>) -> Result<String, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    // Create sample packages
    let package1 = NewPackage {
        name: "Sample Package 1".to_string(),
        description: Some("A sample package for testing".to_string()),
    };
    let package2 = NewPackage {
        name: "Sample Package 2".to_string(),
        description: Some("Another sample package".to_string()),
    };

    let pkg1 = PackageService::create(&mut *conn, package1).map_err(|e| e.to_string())?;
    let pkg2 = PackageService::create(&mut *conn, package2).map_err(|e| e.to_string())?;

    // Create sample items
    let item1 = NewItem {
        name: "Sample Item 1".to_string(),
        sku: "ITEM-001".to_string(),
    };
    let item2 = NewItem {
        name: "Sample Item 2".to_string(),
        sku: "ITEM-002".to_string(),
    };
    let item3 = NewItem {
        name: "Sample Item 3".to_string(),
        sku: "ITEM-003".to_string(),
    };

    let itm1 = ItemService::create(&mut *conn, item1).map_err(|e| e.to_string())?;
    let itm2 = ItemService::create(&mut *conn, item2).map_err(|e| e.to_string())?;
    let itm3 = ItemService::create(&mut *conn, item3).map_err(|e| e.to_string())?;

    // Create sample recipes
    let recipe1 = NewRecipe {
        package_id: pkg1.id,
        item_id: itm1.id,
        quantity: 10.0,
        unit: "pieces".to_string(),
        valid_from: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
        valid_to: None,
    };
    let recipe2 = NewRecipe {
        package_id: pkg1.id,
        item_id: itm2.id,
        quantity: 5.0,
        unit: "kg".to_string(),
        valid_from: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
        valid_to: Some(NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
    };
    let recipe3 = NewRecipe {
        package_id: pkg2.id,
        item_id: itm3.id,
        quantity: 2.5,
        unit: "liters".to_string(),
        valid_from: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
        valid_to: None,
    };

    RecipeService::create(&mut *conn, recipe1).map_err(|e| e.to_string())?;
    RecipeService::create(&mut *conn, recipe2).map_err(|e| e.to_string())?;
    RecipeService::create(&mut *conn, recipe3).map_err(|e| e.to_string())?;

    // Create sample batches
    let batch1 = NewBatch {
        item_id: itm1.id,
        batch_number: "BATCH-001".to_string(),
        in_date: NaiveDate::from_ymd_opt(2024, 7, 1).unwrap(),
        expiry_date: Some(NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
    };
    let batch2 = NewBatch {
        item_id: itm2.id,
        batch_number: "BATCH-002".to_string(),
        in_date: NaiveDate::from_ymd_opt(2024, 7, 2).unwrap(),
        expiry_date: Some(NaiveDate::from_ymd_opt(2024, 11, 30).unwrap()),
    };
    let batch3 = NewBatch {
        item_id: itm3.id,
        batch_number: "BATCH-003".to_string(),
        in_date: NaiveDate::from_ymd_opt(2024, 7, 3).unwrap(),
        expiry_date: None,
    };

    BatchService::create(&mut *conn, batch1).map_err(|e| e.to_string())?;
    BatchService::create(&mut *conn, batch2).map_err(|e| e.to_string())?;
    BatchService::create(&mut *conn, batch3).map_err(|e| e.to_string())?;

    Ok("Sample data created successfully".to_string())
}
