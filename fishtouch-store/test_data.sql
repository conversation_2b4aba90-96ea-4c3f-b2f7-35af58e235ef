-- 添加测试数据
-- 插入更多套餐
INSERT OR IGNORE INTO packages (id, name, description) VALUES 
(2, '鸡肉汉堡套餐', '包含鸡肉汉堡、薯条和饮料'),
(3, '鱼肉汉堡套餐', '包含鱼肉汉堡、薯条和饮料');

-- 插入更多单品
INSERT OR IGNORE INTO items (id, name, sku) VALUES 
(2, '汉堡肉饼', 'PATTY001'),
(3, '汉堡面包', 'BUN001'),
(4, '生菜', 'LETTUCE001'),
(5, '番茄', 'TOMATO001'),
(6, '可乐', 'COLA001');

-- 插入配方数据
INSERT OR IGNORE INTO recipes (id, package_id, item_id, quantity, unit, valid_from, valid_to) VALUES 
(1, 1, 1, 1.0, '份', '2025-01-01', NULL),
(2, 1, 2, 1.0, '个', '2025-01-01', NULL),
(3, 1, 3, 2.0, '片', '2025-01-01', NULL),
(4, 1, 6, 1.0, '杯', '2025-01-01', NULL),
(5, 2, 2, 1.0, '个', '2025-01-01', NULL),
(6, 2, 1, 1.0, '份', '2025-01-01', NULL),
(7, 2, 6, 1.0, '杯', '2025-01-01', NULL);

-- 插入批次数据
INSERT OR IGNORE INTO batches (id, item_id, batch_number, in_date, expiry_date) VALUES 
(1, 1, 'BATCH001', '2025-07-01', '2025-07-15'),
(2, 2, 'BATCH002', '2025-07-02', '2025-07-16'),
(3, 3, 'BATCH003', '2025-07-03', '2025-07-17'),
(4, 4, 'BATCH004', '2025-07-04', '2025-07-18'),
(5, 5, 'BATCH005', '2025-07-05', '2025-07-19'),
(6, 6, 'BATCH006', '2025-07-06', '2025-08-06');
