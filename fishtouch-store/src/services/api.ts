import { invoke } from "@tauri-apps/api/core";
import type {
  Package, NewPackage,
  Item, NewItem,
  Recipe, NewRecipe,
  Batch, NewBatch,
  PurchaseOrder, NewPurchaseOrder,
  ShipmentOrder, NewShipmentOrder,
  InventoryTransaction,
  StockLevel,
  LowStockAlert
} from "../types";

// 简化的API调用函数 - 直接使用Tauri API
const apiCall = async (command: string, args?: any): Promise<any> => {
  console.log(`🔧 API调用: ${command}`, args);
  
  try {
    const result = await invoke(command, args);
    console.log(`✅ API调用成功: ${command}`, result);
    return result;
  } catch (error) {
    console.error(`❌ API调用失败: ${command}`, error);
    throw error;
  }
};

// 套餐管理 API
export const packageApi = {
  async create(data: NewPackage): Promise<Package> {
    return await apiCall("create_package", {
      name: data.name,
      description: data.description
    });
  },

  async getAll(): Promise<Package[]> {
    return await apiCall("get_all_packages");
  },

  async getById(id: number): Promise<Package> {
    return await apiCall("get_package_by_id", { id });
  },

  async update(id: number, data: NewPackage): Promise<Package> {
    return await apiCall("update_package", {
      id,
      name: data.name,
      description: data.description
    });
  },

  async delete(id: number): Promise<number> {
    return await apiCall("delete_package", { id });
  }
};

// 单品管理 API
export const itemApi = {
  async create(data: NewItem): Promise<Item> {
    return await apiCall("create_item", {
      name: data.name,
      sku: data.sku
    });
  },

  async getAll(): Promise<Item[]> {
    return await apiCall("get_all_items");
  },

  async getById(id: number): Promise<Item> {
    return await apiCall("get_item_by_id", { id });
  },

  async getBySku(sku: string): Promise<Item> {
    return await apiCall("get_item_by_sku", { sku });
  },

  async update(id: number, data: Partial<NewItem>): Promise<Item> {
    return await apiCall("update_item", {
      id,
      name: data.name,
      sku: data.sku
    });
  },

  async delete(id: number): Promise<number> {
    return await apiCall("delete_item", { id });
  }
};

// 配方管理 API
export const recipeApi = {
  async create(data: NewRecipe): Promise<Recipe> {
    return await apiCall("create_recipe", {
      package_id: data.package_id,
      item_id: data.item_id,
      quantity: data.quantity,
      unit: data.unit,
      valid_from: data.valid_from,
      valid_to: data.valid_to
    });
  },

  async getByPackageId(packageId: number): Promise<Recipe[]> {
    return await apiCall("get_recipes_by_package_id", { package_id: packageId });
  },

  async getAll(): Promise<Recipe[]> {
    return await apiCall("get_all_recipes");
  },

  async getById(id: number): Promise<Recipe> {
    return await apiCall("get_recipe_by_id", { id });
  },

  async update(id: number, data: Partial<NewRecipe>): Promise<Recipe> {
    return await apiCall("update_recipe", {
      id,
      quantity: data.quantity,
      unit: data.unit,
      valid_from: data.valid_from,
      valid_to: data.valid_to
    });
  },

  async delete(id: number): Promise<number> {
    return await apiCall("delete_recipe", { id });
  }
};

// 批次管理 API
export const batchApi = {
  async create(data: NewBatch): Promise<Batch> {
    return await apiCall("create_batch", {
      item_id: data.item_id,
      batch_number: data.batch_number,
      in_date: data.in_date,
      expiry_date: data.expiry_date
    });
  },

  async getByItemId(itemId: number): Promise<Batch[]> {
    return await apiCall("get_batches_by_item_id", { item_id: itemId });
  },

  async getAll(): Promise<Batch[]> {
    return await apiCall("get_all_batches");
  },

  async getById(id: number): Promise<Batch> {
    return await apiCall("get_batch_by_id", { id });
  },

  async update(id: number, data: Partial<NewBatch>): Promise<Batch> {
    return await apiCall("update_batch", {
      id,
      batch_number: data.batch_number,
      in_date: data.in_date,
      expiry_date: data.expiry_date
    });
  },

  async delete(id: number): Promise<number> {
    return await apiCall("delete_batch", { id });
  }
};

// 采购订单 API
export const purchaseOrderApi = {
  async create(data: NewPurchaseOrder): Promise<PurchaseOrder> {
    return await apiCall("create_purchase_order", {
      order_number: data.order_number,
      order_date: data.order_date,
      supplier: data.supplier
    });
  }
};

// 发货订单 API
export const shipmentOrderApi = {
  async create(data: NewShipmentOrder): Promise<ShipmentOrder> {
    return await apiCall("create_shipment_order", {
      order_number: data.order_number,
      order_date: data.order_date,
      customer: data.customer
    });
  }
};

// 库存管理 API
export const inventoryApi = {
  async recordPurchaseTransaction(
    batchId: number,
    quantity: number,
    orderNumber: string,
    timestamp: string
  ): Promise<InventoryTransaction> {
    return await apiCall("record_purchase_transaction", {
      batch_id: batchId,
      quantity,
      order_number: orderNumber,
      timestamp
    });
  },

  async recordShipmentTransaction(
    batchId: number,
    quantity: number,
    orderNumber: string,
    timestamp: string
  ): Promise<InventoryTransaction> {
    return await apiCall("record_shipment_transaction", {
      batch_id: batchId,
      quantity,
      order_number: orderNumber,
      timestamp
    });
  },

  async getCurrentStockForItem(itemId: number): Promise<StockLevel> {
    return await apiCall("get_current_stock_for_item", { item_id: itemId });
  },

  async getAllStockLevels(): Promise<StockLevel[]> {
    return await apiCall("get_all_stock_levels");
  },

  async checkLowStockAlerts(): Promise<LowStockAlert[]> {
    return await apiCall("check_low_stock_alerts");
  }
};

// 测试数据 API
export const testApi = {
  async createSampleData(): Promise<string> {
    return await apiCall("create_sample_data");
  }
};
