import React from "react";
import {
  Box,
} from "@chakra-ui/react";
import Sidebar from "./Sidebar";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <Box minH="100vh" bg="gray.50">
      <Sidebar />
      <Box ml={{ base: 0, md: 60 }}>
        <Box p={4}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default MainLayout;
