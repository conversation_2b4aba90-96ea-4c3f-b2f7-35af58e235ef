import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Heading,
  Stack,
  Table,
  Text,
  Input,
  Alert,
  Badge,
  Select,
  Portal,
  createListCollection,
} from "@chakra-ui/react";
import { Plus, Edit, Trash2 } from "lucide-react";
import { batchApi, itemApi } from "../services/api";
import { toaster } from "../components/ui/toaster";
import type { Batch, NewBatch, Item } from "../types";

const BatchManagement: React.FC = () => {
  const [batches, setBatches] = useState<Batch[]>([]);
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);

  // 创建单品选择的collection
  const itemCollection = createListCollection({
    items: items.map(item => ({
      label: `${item.name} (${item.sku})`,
      value: item.id.toString(),
      id: item.id
    })),
  });

  // 表单状态
  const [formData, setFormData] = useState<NewBatch>({
    item_id: 0,
    batch_number: "",
    in_date: "",
    expiry_date: undefined,
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedItemId) {
      fetchBatchesByItem(selectedItemId);
    }
  }, [selectedItemId]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      const itemsData = await itemApi.getAll();
      setItems(itemsData);
      if (itemsData.length > 0) {
        setSelectedItemId(itemsData[0].id);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取数据失败";
      setError(errorMessage);
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchBatchesByItem = async (itemId: number) => {
    try {
      const batchesData = await batchApi.getByItemId(itemId);
      setBatches(batchesData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取批次失败";
      setError(errorMessage);
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    }
  };

  const handleCreate = async () => {
    try {
      if (!selectedItemId) {
        toaster.create({
          title: "错误",
          description: "请选择单品",
          type: "error",
          duration: 3000,
          closable: true,
        });
        return;
      }

      const newBatch = {
        ...formData,
        item_id: selectedItemId,
      };

      await batchApi.create(newBatch);
      toaster.create({
        title: "成功",
        description: "批次创建成功",
        type: "success",
        duration: 3000,
        closable: true,
      });

      setShowCreateForm(false);
      setFormData({
        item_id: 0,
        batch_number: "",
        in_date: "",
        expiry_date: undefined,
      });

      if (selectedItemId) {
        fetchBatchesByItem(selectedItemId);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "创建批次失败";
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    }
  };

  const getItemName = (itemId: number) => {
    const item = items.find(i => i.id === itemId);
    return item ? `${item.name} (${item.sku})` : "未知单品";
  };

  const getExpiryStatus = (expiryDate: string | null | undefined) => {
    if (!expiryDate) return { status: "无期限", color: "gray" };

    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(now.getDate() + 30);

    if (expiry < now) {
      return { status: "已过期", color: "red" };
    } else if (expiry <= thirtyDaysFromNow) {
      return { status: "即将过期", color: "orange" };
    } else {
      return { status: "正常", color: "green" };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>批次管理</Heading>
        <Text>加载中...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Heading size="lg" mb={6}>批次管理</Heading>
        <Alert.Root status="error">
          <Alert.Indicator />
          <Alert.Content>
            <Alert.Title>错误!</Alert.Title>
            <Alert.Description>{error}</Alert.Description>
          </Alert.Content>
        </Alert.Root>
      </Box>
    );
  }

  return (
    <Box>
      <Stack direction="row" justify="space-between" align="center" mb={6}>
        <Heading size="lg">批次管理</Heading>
        <Button
          colorPalette="blue"
          onClick={() => setShowCreateForm(!showCreateForm)}
        >
          <Plus size={16} />
          添加批次
        </Button>
      </Stack>

      {/* 单品选择器 */}
      <Box mb={6}>
        <Select.Root
          collection={itemCollection}
          size="sm"
          width="100%"
          value={selectedItemId ? [selectedItemId.toString()] : []}
          onValueChange={(details) => {
            const value = details.value[0];
            setSelectedItemId(value ? Number(value) : null);
          }}
        >
          <Select.Label>选择单品:</Select.Label>
          <Select.Control>
            <Select.Trigger>
              <Select.ValueText placeholder="选择单品" />
            </Select.Trigger>
            <Select.IndicatorGroup>
              <Select.Indicator />
            </Select.IndicatorGroup>
          </Select.Control>
          <Portal>
            <Select.Positioner>
              <Select.Content>
                {itemCollection.items.map((item) => (
                  <Select.Item item={item} key={item.value}>
                    {item.label}
                    <Select.ItemIndicator />
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Positioner>
          </Portal>
        </Select.Root>
      </Box>

      {/* 创建表单 */}
      {showCreateForm && (
        <Box p={4} border="1px" borderColor="gray.200" borderRadius="md" mb={6}>
          <Heading size="md" mb={4}>添加新批次</Heading>
          <Stack direction="column" gap={4}>
            <Box>
              <Text mb={2}>批次号:</Text>
              <Input
                value={formData.batch_number}
                onChange={(e) => setFormData({ ...formData, batch_number: e.target.value })}
                placeholder="输入批次号"
              />
            </Box>

            <Box>
              <Text mb={2}>入库日期:</Text>
              <Input
                type="date"
                value={formData.in_date}
                onChange={(e) => setFormData({ ...formData, in_date: e.target.value })}
              />
            </Box>

            <Box>
              <Text mb={2}>过期日期 (可选):</Text>
              <Input
                type="date"
                value={formData.expiry_date || ""}
                onChange={(e) => setFormData({ ...formData, expiry_date: e.target.value || undefined })}
              />
            </Box>

            <Stack direction="row" gap={2}>
              <Button colorPalette="blue" onClick={handleCreate}>
                创建批次
              </Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                取消
              </Button>
            </Stack>
          </Stack>
        </Box>
      )}

      {/* 批次列表 */}
      {selectedItemId && (
        <Box>
          <Text mb={4} fontSize="lg" fontWeight="medium">
            {getItemName(selectedItemId)} 的批次列表
          </Text>

          {batches.length === 0 ? (
            <Text color="gray.500">该单品暂无批次</Text>
          ) : (
            <Table.Root>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeader>批次号</Table.ColumnHeader>
                  <Table.ColumnHeader>入库日期</Table.ColumnHeader>
                  <Table.ColumnHeader>过期日期</Table.ColumnHeader>
                  <Table.ColumnHeader>状态</Table.ColumnHeader>
                  <Table.ColumnHeader>操作</Table.ColumnHeader>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {batches.map((batch) => {
                  const expiryStatus = getExpiryStatus(batch.expiry_date);
                  return (
                    <Table.Row key={batch.id}>
                      <Table.Cell fontWeight="medium">{batch.batch_number}</Table.Cell>
                      <Table.Cell>{formatDate(batch.in_date)}</Table.Cell>
                      <Table.Cell>
                        {batch.expiry_date ? formatDate(batch.expiry_date) : "无期限"}
                      </Table.Cell>
                      <Table.Cell>
                        <Badge colorPalette={expiryStatus.color}>
                          {expiryStatus.status}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <Stack direction="row" gap={2}>
                          <Button size="sm" variant="outline">
                            <Edit size={14} />
                          </Button>
                          <Button size="sm" colorPalette="red" variant="outline">
                            <Trash2 size={14} />
                          </Button>
                        </Stack>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table.Root>
          )}
        </Box>
      )}
    </Box>
  );
};

export default BatchManagement;
