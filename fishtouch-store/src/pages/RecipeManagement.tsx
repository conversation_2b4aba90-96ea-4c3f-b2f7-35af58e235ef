import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Heading,
  Stack,
  Table,
  Text,
  Input,
  Alert,
  Select,
  Portal,
  createListCollection,
} from "@chakra-ui/react";
import { Plus, Edit, Trash2 } from "lucide-react";
import { recipeApi, packageApi, itemApi } from "../services/api";
import { toaster } from "../components/ui/toaster";
import type { Recipe, NewRecipe, Package, Item } from "../types";

const RecipeManagement: React.FC = () => {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [packages, setPackages] = useState<Package[]>([]);
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedPackageId, setSelectedPackageId] = useState<number | null>(null);

  // 创建套餐选择的collection
  const packageCollection = createListCollection({
    items: packages.map(pkg => ({
      label: pkg.name,
      value: pkg.id.toString(),
      id: pkg.id
    })),
  });

  // 创建单品选择的collection
  const itemCollection = createListCollection({
    items: items.map(item => ({
      label: `${item.name} (${item.sku})`,
      value: item.id.toString(),
      id: item.id
    })),
  });

  // 表单状态
  const [formData, setFormData] = useState<NewRecipe>({
    package_id: 0,
    item_id: 0,
    quantity: 0,
    unit: "",
    valid_from: "",
    valid_to: undefined,
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedPackageId) {
      fetchRecipesByPackage(selectedPackageId);
    }
  }, [selectedPackageId]);

  const fetchInitialData = async () => {
    try {
      setLoading(true);
      const [packagesData, itemsData] = await Promise.all([
        packageApi.getAll(),
        itemApi.getAll(),
      ]);
      setPackages(packagesData);
      setItems(itemsData);
      if (packagesData.length > 0) {
        setSelectedPackageId(packagesData[0].id);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取数据失败";
      setError(errorMessage);
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchRecipesByPackage = async (packageId: number) => {
    try {
      const recipesData = await recipeApi.getByPackageId(packageId);
      setRecipes(recipesData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取配方失败";
      setError(errorMessage);
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    }
  };

  const handleCreate = async () => {
    try {
      if (!selectedPackageId) {
        toaster.create({
          title: "错误",
          description: "请选择套餐",
          type: "error",
          duration: 3000,
          closable: true,
        });
        return;
      }

      const newRecipe = {
        ...formData,
        package_id: selectedPackageId,
      };

      await recipeApi.create(newRecipe);
      toaster.create({
        title: "成功",
        description: "配方创建成功",
        type: "success",
        duration: 3000,
        closable: true,
      });

      setShowCreateForm(false);
      setFormData({
        package_id: 0,
        item_id: 0,
        quantity: 0,
        unit: "",
        valid_from: "",
        valid_to: undefined,
      });

      if (selectedPackageId) {
        fetchRecipesByPackage(selectedPackageId);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "创建配方失败";
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    }
  };

  const getItemName = (itemId: number) => {
    const item = items.find(i => i.id === itemId);
    return item ? item.name : "未知单品";
  };

  const getPackageName = (packageId: number) => {
    const pkg = packages.find(p => p.id === packageId);
    return pkg ? pkg.name : "未知套餐";
  };

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>配方管理</Heading>
        <Text>加载中...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Heading size="lg" mb={6}>配方管理</Heading>
        <Alert.Root status="error">
          <Alert.Indicator />
          <Alert.Content>
            <Alert.Title>错误!</Alert.Title>
            <Alert.Description>{error}</Alert.Description>
          </Alert.Content>
        </Alert.Root>
      </Box>
    );
  }

  return (
    <Box>
      <Stack direction="row" justify="space-between" align="center" mb={6}>
        <Heading size="lg">配方管理</Heading>
        <Button
          colorPalette="blue"
          onClick={() => setShowCreateForm(!showCreateForm)}
        >
          <Plus size={16} />
          添加配方
        </Button>
      </Stack>

      {/* 套餐选择器 */}
      <Box mb={6}>
        <Select.Root
          collection={packageCollection}
          size="sm"
          width="100%"
          value={selectedPackageId ? [selectedPackageId.toString()] : []}
          onValueChange={(details) => {
            const value = details.value[0];
            setSelectedPackageId(value ? Number(value) : null);
          }}
        >
          <Select.Label>选择套餐:</Select.Label>
          <Select.Control>
            <Select.Trigger>
              <Select.ValueText placeholder="选择套餐" />
            </Select.Trigger>
            <Select.IndicatorGroup>
              <Select.Indicator />
            </Select.IndicatorGroup>
          </Select.Control>
          <Portal>
            <Select.Positioner>
              <Select.Content>
                {packageCollection.items.map((pkg) => (
                  <Select.Item item={pkg} key={pkg.value}>
                    {pkg.label}
                    <Select.ItemIndicator />
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Positioner>
          </Portal>
        </Select.Root>
      </Box>

      {/* 创建表单 */}
      {showCreateForm && (
        <Box p={4} border="1px" borderColor="gray.200" borderRadius="md" mb={6}>
          <Heading size="md" mb={4}>添加新配方</Heading>
          <Stack direction="column" gap={4}>
            <Box>
              <Select.Root
                collection={itemCollection}
                size="sm"
                width="100%"
                value={formData.item_id ? [formData.item_id.toString()] : []}
                onValueChange={(details) => {
                  const value = details.value[0];
                  setFormData({ ...formData, item_id: value ? Number(value) : 0 });
                }}
              >
                <Select.Label>单品:</Select.Label>
                <Select.Control>
                  <Select.Trigger>
                    <Select.ValueText placeholder="选择单品" />
                  </Select.Trigger>
                  <Select.IndicatorGroup>
                    <Select.Indicator />
                  </Select.IndicatorGroup>
                </Select.Control>
                <Portal>
                  <Select.Positioner>
                    <Select.Content>
                      {itemCollection.items.map((item) => (
                        <Select.Item item={item} key={item.value}>
                          {item.label}
                          <Select.ItemIndicator />
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select.Positioner>
                </Portal>
              </Select.Root>
            </Box>

            <Box>
              <Text mb={2}>数量:</Text>
              <Input
                type="number"
                step="0.01"
                value={formData.quantity}
                onChange={(e) => setFormData({ ...formData, quantity: parseFloat(e.target.value) || 0 })}
                placeholder="输入数量"
              />
            </Box>

            <Box>
              <Text mb={2}>单位:</Text>
              <Input
                value={formData.unit}
                onChange={(e) => setFormData({ ...formData, unit: e.target.value })}
                placeholder="输入单位 (如: 个, 克, 毫升)"
              />
            </Box>

            <Box>
              <Text mb={2}>生效日期:</Text>
              <Input
                type="date"
                value={formData.valid_from}
                onChange={(e) => setFormData({ ...formData, valid_from: e.target.value })}
              />
            </Box>

            <Box>
              <Text mb={2}>失效日期 (可选):</Text>
              <Input
                type="date"
                value={formData.valid_to || ""}
                onChange={(e) => setFormData({ ...formData, valid_to: e.target.value || undefined })}
              />
            </Box>

            <Stack direction="row" gap={2}>
              <Button colorPalette="blue" onClick={handleCreate}>
                创建配方
              </Button>
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                取消
              </Button>
            </Stack>
          </Stack>
        </Box>
      )}

      {/* 配方列表 */}
      {selectedPackageId && (
        <Box>
          <Text mb={4} fontSize="lg" fontWeight="medium">
            {getPackageName(selectedPackageId)} 的配方列表
          </Text>

          {recipes.length === 0 ? (
            <Text color="gray.500">该套餐暂无配方</Text>
          ) : (
            <Table.Root>
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeader>单品</Table.ColumnHeader>
                  <Table.ColumnHeader>数量</Table.ColumnHeader>
                  <Table.ColumnHeader>单位</Table.ColumnHeader>
                  <Table.ColumnHeader>生效日期</Table.ColumnHeader>
                  <Table.ColumnHeader>失效日期</Table.ColumnHeader>
                  <Table.ColumnHeader>操作</Table.ColumnHeader>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {recipes.map((recipe) => (
                  <Table.Row key={recipe.id}>
                    <Table.Cell>{getItemName(recipe.item_id)}</Table.Cell>
                    <Table.Cell>{recipe.quantity}</Table.Cell>
                    <Table.Cell>{recipe.unit}</Table.Cell>
                    <Table.Cell>{recipe.valid_from}</Table.Cell>
                    <Table.Cell>{recipe.valid_to || "无限期"}</Table.Cell>
                    <Table.Cell>
                      <Stack direction="row" gap={2}>
                        <Button size="sm" variant="outline">
                          <Edit size={14} />
                        </Button>
                        <Button size="sm" colorPalette="red" variant="outline">
                          <Trash2 size={14} />
                        </Button>
                      </Stack>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table.Root>
          )}
        </Box>
      )}
    </Box>
  );
};

export default RecipeManagement;
