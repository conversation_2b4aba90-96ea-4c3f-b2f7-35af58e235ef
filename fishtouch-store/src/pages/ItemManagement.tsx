import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Heading,
  Table,
  useDisclosure,
  Al<PERSON>,
  Stack,
  IconButton,
  Text,
  Spinner,
  Center,
  Badge,
  Input,
  InputGroup,
} from "@chakra-ui/react";
import { Plus, Edit, Trash2, Search } from "lucide-react";
import { itemApi } from "../services/api";
import type { Item } from "../types";
import ItemModal from "../components/Item/ItemModal";
import DeleteConfirmDialog from "../components/Common/DeleteConfirmDialog";
import { toaster } from "../components/ui/toaster";

const ItemManagement: React.FC = () => {
  const [items, setItems] = useState<Item[]>([]);
  const [filteredItems, setFilteredItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [deleteItem, setDeleteItem] = useState<Item | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const { open: isModalOpen, onOpen: onModalOpen, onClose: onModalClose } = useDisclosure();
  const { open: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();

  // TODO: 实现 toast 功能
  // const toast = useToast();

  const fetchItems = async () => {
    try {
      console.log("🔧 开始获取单品列表...");
      setLoading(true);
      setError(null);
      const data = await itemApi.getAll();
      console.log("✅ 单品数据获取成功:", data);
      setItems(data);
      setFilteredItems(data);
    } catch (err) {
      console.error("❌ 获取单品列表失败:", err);
      const errorMessage = err instanceof Error ? err.message : "获取单品列表失败";
      setError(errorMessage);
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  // 搜索过滤
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredItems(items);
    } else {
      const filtered = items.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredItems(filtered);
    }
  }, [searchTerm, items]);

  const handleCreate = () => {
    setSelectedItem(null);
    onModalOpen();
  };

  const handleEdit = (item: Item) => {
    setSelectedItem(item);
    onModalOpen();
  };

  const handleDeleteClick = (item: Item) => {
    setDeleteItem(item);
    onDeleteOpen();
  };

  const handleDeleteConfirm = async () => {
    if (!deleteItem) return;

    try {
      await itemApi.delete(deleteItem.id);
      toaster.create({
        title: "成功",
        description: "单品删除成功",
        type: "success",
        duration: 3000,
        closable: true,
      });
      fetchItems();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "删除单品失败";
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    } finally {
      setDeleteItem(null);
      onDeleteClose();
    }
  };

  const handleModalSuccess = () => {
    onModalClose();
    fetchItems();
  };

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          单品管理
        </Heading>
        <Center>
          <Spinner size="xl" />
        </Center>
      </Box>
    );
  }

  return (
    <Box>
      <Stack direction="row" justify="space-between" mb={6}>
        <Heading size="lg">单品管理</Heading>
        <Button colorPalette="blue" onClick={handleCreate}>
          <Plus />
          新增单品
        </Button>
      </Stack>

      {error && (
        <Alert.Root status="error" mb={6}>
          <Alert.Indicator />
          <Alert.Content>
            <Alert.Title>错误!</Alert.Title>
            <Alert.Description>{error}</Alert.Description>
          </Alert.Content>
        </Alert.Root>
      )}

      {/* 搜索框 */}
      <Box mb={6}>
        <InputGroup maxW="400px" startElement={<Search />}>
          <Input
            placeholder="搜索单品名称或SKU..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </InputGroup>
      </Box>

      <Table.Root variant="outline">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader>ID</Table.ColumnHeader>
            <Table.ColumnHeader>单品名称</Table.ColumnHeader>
            <Table.ColumnHeader>SKU</Table.ColumnHeader>
            <Table.ColumnHeader>状态</Table.ColumnHeader>
            <Table.ColumnHeader>操作</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {filteredItems.length === 0 ? (
            <Table.Row>
              <Table.Cell colSpan={5}>
                <Center>
                  <Text color="gray.500">
                    {searchTerm ? "未找到匹配的单品" : "暂无单品数据"}
                  </Text>
                </Center>
              </Table.Cell>
            </Table.Row>
          ) : (
            filteredItems.map((item) => (
              <Table.Row key={item.id}>
                <Table.Cell>{item.id}</Table.Cell>
                <Table.Cell fontWeight="medium">{item.name}</Table.Cell>
                <Table.Cell>
                  <Badge colorPalette="blue" variant="subtle">
                    {item.sku}
                  </Badge>
                </Table.Cell>
                <Table.Cell>
                  <Badge colorPalette="green">正常</Badge>
                </Table.Cell>
                <Table.Cell>
                  <Stack direction="row" gap={2}>
                    <IconButton
                      aria-label="编辑单品"
                      size="sm"
                      colorPalette="blue"
                      variant="ghost"
                      onClick={() => handleEdit(item)}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      aria-label="删除单品"
                      size="sm"
                      colorPalette="red"
                      variant="ghost"
                      onClick={() => handleDeleteClick(item)}
                    >
                      <Trash2 />
                    </IconButton>
                  </Stack>
                </Table.Cell>
              </Table.Row>
            ))
          )}
        </Table.Body>
      </Table.Root>

      {/* 显示搜索结果统计 */}
      {searchTerm && (
        <Box mt={4}>
          <Text fontSize="sm" color="gray.600">
            找到 {filteredItems.length} 个匹配的单品
          </Text>
        </Box>
      )}

      {/* 单品创建/编辑模态框 */}
      <ItemModal
        open={isModalOpen}
        onOpenChange={(open) => !open && onModalClose()}
        item={selectedItem}
        onSuccess={handleModalSuccess}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={isDeleteOpen}
        onOpenChange={(open) => !open && onDeleteClose()}
        onConfirm={handleDeleteConfirm}
        title="删除单品"
        description={`确定要删除单品 "${deleteItem?.name}" (SKU: ${deleteItem?.sku}) 吗？此操作不可撤销。`}
      />
    </Box>
  );
};

export default ItemManagement;
