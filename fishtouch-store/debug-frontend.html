<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API调试工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 FishTouch Store 前端API调试工具</h1>
        <p>这个工具用于测试前端与Tauri后端的API连接。请在Tauri应用中打开此页面。</p>
        
        <div id="environment-check" class="status info">
            <strong>环境检查:</strong> <span id="env-status">检查中...</span>
        </div>
    </div>

    <div class="container">
        <h2>🧪 API测试</h2>
        
        <div class="test-section">
            <h3>基础连接测试</h3>
            <button onclick="testTauriConnection()">测试Tauri连接</button>
            <button onclick="testCreateSampleData()">创建示例数据</button>
            <div id="basic-test-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>套餐管理测试</h3>
            <button onclick="testPackageOperations()">测试套餐CRUD</button>
            <button onclick="getAllPackages()">获取所有套餐</button>
            <div id="package-test-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>单品管理测试</h3>
            <button onclick="testItemOperations()">测试单品CRUD</button>
            <button onclick="getAllItems()">获取所有单品</button>
            <div id="item-test-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>库存管理测试</h3>
            <button onclick="testInventoryOperations()">测试库存操作</button>
            <button onclick="getStockLevels()">获取库存水平</button>
            <button onclick="checkLowStockAlerts()">检查低库存预警</button>
            <div id="inventory-test-log" class="log"></div>
        </div>

        <div class="test-section">
            <h3>综合测试</h3>
            <button onclick="runAllTests()">运行所有测试</button>
            <button onclick="clearAllLogs()">清空日志</button>
            <div id="comprehensive-test-log" class="log"></div>
        </div>
    </div>

    <script>
        // 日志工具函数
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            element.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLog(elementId) {
            document.getElementById(elementId).textContent = '';
        }

        function clearAllLogs() {
            const logElements = ['basic-test-log', 'package-test-log', 'item-test-log', 'inventory-test-log', 'comprehensive-test-log'];
            logElements.forEach(clearLog);
        }

        // 环境检查
        function checkEnvironment() {
            const envStatus = document.getElementById('env-status');
            const envCheck = document.getElementById('environment-check');
            
            if (typeof window.__TAURI__ !== 'undefined') {
                envStatus.textContent = 'Tauri环境已检测到 ✅';
                envCheck.className = 'status success';
            } else {
                envStatus.textContent = '未检测到Tauri环境 ❌ (请在Tauri应用中打开)';
                envCheck.className = 'status error';
            }
        }

        // 基础连接测试
        async function testTauriConnection() {
            clearLog('basic-test-log');
            log('basic-test-log', '开始测试Tauri连接...');
            
            try {
                if (!window.__TAURI__) {
                    throw new Error('Tauri环境未检测到');
                }
                
                const result = await window.__TAURI__.core.invoke('greet', { name: '测试用户' });
                log('basic-test-log', `Tauri连接成功: ${result}`, 'success');
            } catch (error) {
                log('basic-test-log', `Tauri连接失败: ${error.message}`, 'error');
            }
        }

        async function testCreateSampleData() {
            log('basic-test-log', '开始创建示例数据...');
            
            try {
                const result = await window.__TAURI__.core.invoke('create_sample_data');
                log('basic-test-log', `示例数据创建成功: ${result}`, 'success');
            } catch (error) {
                log('basic-test-log', `示例数据创建失败: ${error.message}`, 'error');
            }
        }

        // 套餐测试
        async function testPackageOperations() {
            clearLog('package-test-log');
            log('package-test-log', '开始测试套餐CRUD操作...');
            
            try {
                // 创建套餐
                const newPackage = await window.__TAURI__.core.invoke('create_package', {
                    name: '测试套餐_' + Date.now(),
                    description: '这是一个测试套餐'
                });
                log('package-test-log', `套餐创建成功: ID=${newPackage.id}, 名称=${newPackage.name}`, 'success');
                
                // 获取套餐
                const retrievedPackage = await window.__TAURI__.core.invoke('get_package_by_id', { id: newPackage.id });
                log('package-test-log', `套餐查询成功: ${retrievedPackage.name}`, 'success');
                
                // 更新套餐
                const updatedPackage = await window.__TAURI__.core.invoke('update_package', {
                    id: newPackage.id,
                    name: '更新的测试套餐',
                    description: '更新的描述'
                });
                log('package-test-log', `套餐更新成功: ${updatedPackage.name}`, 'success');
                
                // 删除套餐
                const deleteResult = await window.__TAURI__.core.invoke('delete_package', { id: newPackage.id });
                log('package-test-log', `套餐删除成功: 删除了${deleteResult}个`, 'success');
                
            } catch (error) {
                log('package-test-log', `套餐操作失败: ${error.message}`, 'error');
            }
        }

        async function getAllPackages() {
            log('package-test-log', '获取所有套餐...');
            
            try {
                const packages = await window.__TAURI__.core.invoke('get_all_packages');
                log('package-test-log', `获取套餐成功: 共${packages.length}个套餐`, 'success');
                if (packages.length > 0) {
                    log('package-test-log', `套餐列表: ${JSON.stringify(packages, null, 2)}`);
                }
            } catch (error) {
                log('package-test-log', `获取套餐失败: ${error.message}`, 'error');
            }
        }

        // 单品测试
        async function testItemOperations() {
            clearLog('item-test-log');
            log('item-test-log', '开始测试单品CRUD操作...');
            
            try {
                // 创建单品
                const newItem = await window.__TAURI__.core.invoke('create_item', {
                    name: '测试单品_' + Date.now(),
                    sku: 'TEST_' + Date.now()
                });
                log('item-test-log', `单品创建成功: ID=${newItem.id}, 名称=${newItem.name}, SKU=${newItem.sku}`, 'success');
                
                // 获取单品
                const retrievedItem = await window.__TAURI__.core.invoke('get_item_by_id', { id: newItem.id });
                log('item-test-log', `单品查询成功: ${retrievedItem.name}`, 'success');
                
                // 通过SKU获取单品
                const itemBySku = await window.__TAURI__.core.invoke('get_item_by_sku', { sku: newItem.sku });
                log('item-test-log', `通过SKU查询成功: ${itemBySku.name}`, 'success');
                
                // 删除单品
                const deleteResult = await window.__TAURI__.core.invoke('delete_item', { id: newItem.id });
                log('item-test-log', `单品删除成功: 删除了${deleteResult}个`, 'success');
                
            } catch (error) {
                log('item-test-log', `单品操作失败: ${error.message}`, 'error');
            }
        }

        async function getAllItems() {
            log('item-test-log', '获取所有单品...');
            
            try {
                const items = await window.__TAURI__.core.invoke('get_all_items');
                log('item-test-log', `获取单品成功: 共${items.length}个单品`, 'success');
                if (items.length > 0) {
                    log('item-test-log', `单品列表: ${JSON.stringify(items, null, 2)}`);
                }
            } catch (error) {
                log('item-test-log', `获取单品失败: ${error.message}`, 'error');
            }
        }

        // 库存测试
        async function testInventoryOperations() {
            clearLog('inventory-test-log');
            log('inventory-test-log', '开始测试库存操作...');
            
            try {
                // 这里需要先有单品和批次数据
                log('inventory-test-log', '库存操作测试需要先创建示例数据', 'info');
                await testCreateSampleData();
                
                // 获取库存水平
                const stockLevels = await window.__TAURI__.core.invoke('get_all_stock_levels');
                log('inventory-test-log', `获取库存水平成功: 共${stockLevels.length}个单品有库存`, 'success');
                
            } catch (error) {
                log('inventory-test-log', `库存操作失败: ${error.message}`, 'error');
            }
        }

        async function getStockLevels() {
            log('inventory-test-log', '获取库存水平...');
            
            try {
                const stockLevels = await window.__TAURI__.core.invoke('get_all_stock_levels');
                log('inventory-test-log', `库存水平获取成功: 共${stockLevels.length}个单品`, 'success');
                if (stockLevels.length > 0) {
                    log('inventory-test-log', `库存详情: ${JSON.stringify(stockLevels, null, 2)}`);
                }
            } catch (error) {
                log('inventory-test-log', `获取库存水平失败: ${error.message}`, 'error');
            }
        }

        async function checkLowStockAlerts() {
            log('inventory-test-log', '检查低库存预警...');
            
            try {
                const alerts = await window.__TAURI__.core.invoke('check_low_stock_alerts');
                log('inventory-test-log', `低库存预警检查成功: 共${alerts.length}个预警`, 'success');
                if (alerts.length > 0) {
                    log('inventory-test-log', `预警详情: ${JSON.stringify(alerts, null, 2)}`);
                }
            } catch (error) {
                log('inventory-test-log', `检查低库存预警失败: ${error.message}`, 'error');
            }
        }

        // 综合测试
        async function runAllTests() {
            clearLog('comprehensive-test-log');
            log('comprehensive-test-log', '🚀 开始运行所有API测试...');
            
            const tests = [
                { name: 'Tauri连接', func: testTauriConnection },
                { name: '创建示例数据', func: testCreateSampleData },
                { name: '套餐操作', func: testPackageOperations },
                { name: '单品操作', func: testItemOperations },
                { name: '库存操作', func: testInventoryOperations }
            ];
            
            let passedTests = 0;
            
            for (const test of tests) {
                try {
                    log('comprehensive-test-log', `正在运行: ${test.name}...`);
                    await test.func();
                    log('comprehensive-test-log', `✅ ${test.name} 测试通过`, 'success');
                    passedTests++;
                } catch (error) {
                    log('comprehensive-test-log', `❌ ${test.name} 测试失败: ${error.message}`, 'error');
                }
                
                // 添加延迟避免过快执行
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('comprehensive-test-log', `\n📊 测试完成: ${passedTests}/${tests.length} 个测试通过`, passedTests === tests.length ? 'success' : 'error');
            
            if (passedTests === tests.length) {
                log('comprehensive-test-log', '🎉 所有API测试都通过了！前后端连接正常！', 'success');
            } else {
                log('comprehensive-test-log', '⚠️ 部分测试失败，请检查后端实现和连接', 'error');
            }
        }

        // 页面加载时检查环境
        window.addEventListener('load', checkEnvironment);
    </script>
</body>
</html>
