// 测试前端API连接的脚本
// 在浏览器控制台中运行此脚本来测试API连接

console.log("开始测试API连接...");

// 测试创建示例数据
async function testCreateSampleData() {
  try {
    console.log("测试创建示例数据...");
    const result = await window.__TAURI__.core.invoke("create_sample_data");
    console.log("✅ 创建示例数据成功:", result);
    return true;
  } catch (error) {
    console.error("❌ 创建示例数据失败:", error);
    return false;
  }
}

// 测试获取所有套餐
async function testGetAllPackages() {
  try {
    console.log("测试获取所有套餐...");
    const result = await window.__TAURI__.core.invoke("get_all_packages");
    console.log("✅ 获取套餐成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 获取套餐失败:", error);
    return null;
  }
}

// 测试获取所有单品
async function testGetAllItems() {
  try {
    console.log("测试获取所有单品...");
    const result = await window.__TAURI__.core.invoke("get_all_items");
    console.log("✅ 获取单品成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 获取单品失败:", error);
    return null;
  }
}

// 测试获取库存水平
async function testGetStockLevels() {
  try {
    console.log("测试获取库存水平...");
    const result = await window.__TAURI__.core.invoke("get_all_stock_levels");
    console.log("✅ 获取库存水平成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 获取库存水平失败:", error);
    return null;
  }
}

// 测试创建新套餐
async function testCreatePackage() {
  try {
    console.log("测试创建新套餐...");
    const result = await window.__TAURI__.core.invoke("create_package", {
      name: "测试套餐",
      description: "这是一个测试套餐"
    });
    console.log("✅ 创建套餐成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 创建套餐失败:", error);
    return null;
  }
}

// 测试创建新单品
async function testCreateItem() {
  try {
    console.log("测试创建新单品...");
    const result = await window.__TAURI__.core.invoke("create_item", {
      name: "测试单品",
      sku: "TEST-" + Date.now()
    });
    console.log("✅ 创建单品成功:", result);
    return result;
  } catch (error) {
    console.error("❌ 创建单品失败:", error);
    return null;
  }
}

// 运行所有测试
async function runAllTests() {
  console.log("🚀 开始运行所有API测试...");
  
  // 检查Tauri环境
  if (!window.__TAURI__) {
    console.error("❌ Tauri环境未检测到，请在Tauri应用中运行此测试");
    return;
  }
  
  const results = {
    sampleData: await testCreateSampleData(),
    packages: await testGetAllPackages(),
    items: await testGetAllItems(),
    stockLevels: await testGetStockLevels(),
    newPackage: await testCreatePackage(),
    newItem: await testCreateItem()
  };
  
  console.log("📊 测试结果汇总:", results);
  
  const successCount = Object.values(results).filter(r => r !== null && r !== false).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`✨ 测试完成: ${successCount}/${totalTests} 个测试通过`);
  
  if (successCount === totalTests) {
    console.log("🎉 所有API测试都通过了！");
  } else {
    console.log("⚠️ 部分API测试失败，请检查后端连接和实现");
  }
  
  return results;
}

// 导出测试函数供手动调用
window.apiTests = {
  runAllTests,
  testCreateSampleData,
  testGetAllPackages,
  testGetAllItems,
  testGetStockLevels,
  testCreatePackage,
  testCreateItem
};

console.log("📝 API测试脚本已加载。在浏览器控制台中运行 'apiTests.runAllTests()' 来开始测试");
console.log("或者运行单个测试，例如: 'apiTests.testGetAllPackages()'");
