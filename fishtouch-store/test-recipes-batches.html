<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试配方和批次API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>测试配方和批次API</h1>
    
    <div class="section">
        <h2>配方测试</h2>
        <button onclick="testGetAllRecipes()">获取所有配方</button>
        <button onclick="testGetRecipesByPackageId(1)">获取套餐1的配方</button>
        <button onclick="testGetRecipesByPackageId(2)">获取套餐2的配方</button>
        <div id="recipes-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>批次测试</h2>
        <button onclick="testGetAllBatches()">获取所有批次</button>
        <button onclick="testGetBatchesByItemId(1)">获取单品1的批次</button>
        <button onclick="testGetBatchesByItemId(2)">获取单品2的批次</button>
        <div id="batches-result" class="result"></div>
    </div>

    <script>
        // 检查是否在Tauri环境中
        if (typeof window.__TAURI__ === 'undefined') {
            document.body.innerHTML = '<h1>错误：此页面需要在Tauri应用中运行</h1>';
        } else {
            console.log('Tauri environment detected:', window.__TAURI__);
        }

        const { invoke } = window.__TAURI__.core;

        async function testGetAllRecipes() {
            try {
                const result = await invoke('get_all_recipes');
                document.getElementById('recipes-result').innerHTML = 
                    '<h3>所有配方:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('recipes-result').innerHTML = 
                    '<h3>错误:</h3><pre>' + error + '</pre>';
            }
        }

        async function testGetRecipesByPackageId(packageId) {
            try {
                const result = await invoke('get_recipes_by_package_id', { packageId });
                document.getElementById('recipes-result').innerHTML = 
                    '<h3>套餐 ' + packageId + ' 的配方:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('recipes-result').innerHTML = 
                    '<h3>错误:</h3><pre>' + error + '</pre>';
            }
        }

        async function testGetAllBatches() {
            try {
                const result = await invoke('get_all_batches');
                document.getElementById('batches-result').innerHTML = 
                    '<h3>所有批次:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('batches-result').innerHTML = 
                    '<h3>错误:</h3><pre>' + error + '</pre>';
            }
        }

        async function testGetBatchesByItemId(itemId) {
            try {
                const result = await invoke('get_batches_by_item_id', { itemId });
                document.getElementById('batches-result').innerHTML = 
                    '<h3>单品 ' + itemId + ' 的批次:</h3><pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('batches-result').innerHTML = 
                    '<h3>错误:</h3><pre>' + error + '</pre>';
            }
        }
    </script>
</body>
</html>
